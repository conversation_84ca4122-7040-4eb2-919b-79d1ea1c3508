//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"log"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

const TEST_DATABASE_URL = "postgres://postgres@localhost:5432/projects"

var testPool *pgxpool.Pool // Global pool for all tests in this package

// TestMain is the entry point for tests in this package. It sets up the
// database connection pool and closes it after all tests have run.
func TestMain(m *testing.M) {
	databaseUrl := os.Getenv("TEST_DATABASE_URL")
	if databaseUrl == "" {
		log.Println("TEST_DATABASE_URL env var not set; using default value: ", TEST_DATABASE_URL)
		databaseUrl = TEST_DATABASE_URL
	}

	var err error
	testPool, err = pgxpool.New(context.Background(), databaseUrl)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	defer testPool.Close()

	// Run all tests in the package
	exitCode := m.Run()
	os.Exit(exitCode)
}

// TestRelationalDb_Integration_CRUD performs a full create, read, update, and delete
// cycle against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_CRUD(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool)

	// Ensure the database is clean before this test runs.
	// CASCADE is crucial to also truncate tables with foreign keys.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Integration Test", Valid: true},
			Description:      sql.NullString{Valid: false},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			Faucet:           ptr(uuid.New()),
			FloorTile:        ptr(uuid.New()),
			Lighting:         ptr(uuid.New()),
			Mirror:           ptr(uuid.New()),
			Paint:            ptr(uuid.New()),
			ShowerWallTile:   &uuid.Nil,
			ShowerGlass:      &uuid.Nil,
			Toilet:           ptr(uuid.New()),
			TubDoor:          &uuid.Nil,
			Vanity:           ptr(uuid.New()),
		},
	}
	*testDesign.ShowerWallTile = uuid.New()
	*testDesign.ShowerGlass = uuid.New()
	*testDesign.TubDoor = uuid.New()

	// --- 1. Test Create ---
	createdID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Create failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")

	// --- 2. Test Read ---
	readDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after create")
	require.NotNil(t, readDesign)
	require.NotNil(t, readDesign.Created)

	assert.Equal(t, createdID, readDesign.ID)
	assert.Equal(t, testDesign.ProjectID, readDesign.ProjectID)
	assert.Equal(t, testDesign.Title.String, readDesign.Title.String)
	assert.Equal(t, usecases.Herringbone, *readDesign.FloorTilePattern)
	assert.Equal(t, usecases.Modern, *readDesign.Style)
	assert.Equal(t, usecases.Neutral, *readDesign.ColorScheme)
	assert.Equal(t, usecases.Preview, readDesign.Status)
	assert.Equal(t, readDesign.Created, readDesign.LastUpdated)

	// --- 3. Test Update ---
	readDesign.Title = sql.NullString{String: "Updated Title", Valid: true}
	readDesign.FloorTilePattern = ptr(usecases.VerticalStacked)
	readDesign.Style = ptr(usecases.Traditional)
	readDesign.ColorScheme = ptr(usecases.Bold)
	readDesign.Status = usecases.Archived
	readDesign.TubDoorVisible = true
	readDesign.NichesVisible = true

	_, err = db.UpsertDesign(ctx, *readDesign)
	require.NoError(t, err, "Update failed")

	// --- 4. Read again to verify update ---
	updatedDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after update")
	require.NotNil(t, updatedDesign)

	assert.Equal(t, "Updated Title", updatedDesign.Title.String)
	assert.Equal(t, usecases.VerticalStacked, *updatedDesign.FloorTilePattern)
	assert.Equal(t, usecases.Traditional, *updatedDesign.Style)
	assert.Equal(t, usecases.Bold, *updatedDesign.ColorScheme)
	assert.Equal(t, usecases.Archived, updatedDesign.Status)
	assert.True(t, updatedDesign.TubDoorVisible)
	assert.True(t, updatedDesign.NichesVisible)
	assert.NotEqual(t, updatedDesign.Created, updatedDesign.LastUpdated)

	// --- 5. Test Delete ---
	err = db.DeleteDesign(ctx, createdID)
	require.NoError(t, err, "Delete failed")

	// --- 6. Read again to verify delete ---
	deletedDesign, err := db.ReadDesign(ctx, createdID)
	assert.Nil(t, deletedDesign)
	require.Error(t, err, "Read should fail for a deleted design")
	assert.Contains(t, err.Error(), "not found")

	// --- 7. Test DesignsForProject ---
	// Create another design for the same project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FloorTile:        ptr(uuid.New()),
			FloorTilePattern: ptr(usecases.ThirdOffset),
			Toilet:           ptr(uuid.New()),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
			Mirror:           ptr(uuid.New()),
			Lighting:         ptr(uuid.New()),
			Paint:            ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for second design")

	// Fetch both designs for the original project
	designs, err := db.DesignsForProject(ctx, "PRJ-TEST123")
	require.NoError(t, err, "Fetch designs failed")
	assert.Len(t, designs, 2, "Expected 2 designs for the project")

	// --- 8. Test DesignsByProject ---
	// Create another designs for a different project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST456",
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			FloorTile:        ptr(uuid.New()),
			FloorTilePattern: ptr(usecases.VerticalStacked),
			Toilet:           ptr(uuid.New()),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
			Mirror:           ptr(uuid.New()),
			Lighting:         ptr(uuid.New()),
			Paint:            ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for third design")

	// Fetch all designs for both projects
	projectDesigns, err := db.DesignsByProject(ctx, []string{"PRJ-TEST123", "PRJ-TEST456"})
	require.NoError(t, err, "Fetch designs by project failed")
	assert.Len(t, projectDesigns, 2, "Expected designs for 2 projects")
}

// Helper function to get a pointer to a value
func ptr[T any](v T) *T {
	return &v
}
