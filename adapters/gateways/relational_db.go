package gateways

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// Provides access to the Postgres DB used by the new Design service.
// The current schema of this DB is defined in `frameworks/db/schema.sql`.
type RelationalDb struct {
	db *pgxpool.Pool
}

func NewRelationalDb(db *pgxpool.Pool) *RelationalDb {
	return &RelationalDb{db: db}
}

// UpsertDesign persists a Design entity to the database.
// It uses a transaction to ensure atomicity across multiple table insertions.
func (r *RelationalDb) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	if design.ID == uuid.Nil {
		design.ID = uuid.New()
	}
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return uuid.Nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback is a no-op if the transaction is committed.

	// 1. Insert into room_designs
	roomDesignQuery := `
		MERGE INTO design.room_designs 
		USING (VALUES (@id, @project_id, @status, @style, @color_scheme, @title, @description))
			AS source (id, project_id, status, style, color_scheme, title, description)
		ON design.room_designs.id = source.id::uuid
		WHEN MATCHED THEN UPDATE SET
			project_id = source.project_id,
			status = source.status,
			style = source.style::design.style_enum,
			color_scheme = source.color_scheme::design.color_scheme_enum,
			title = source.title,
			description = source.description,
			updated_at = NOW()
		WHEN NOT MATCHED THEN
			INSERT (id, project_id, status, style, color_scheme, title, description)
			VALUES (source.id::uuid, source.project_id, source.status,
					source.style::design.style_enum, source.color_scheme::design.color_scheme_enum,
					source.title, source.description
			)
		`
	_, err = tx.Exec(ctx, roomDesignQuery, pgx.NamedArgs{
		"id":           design.ID,
		"project_id":   design.ProjectID,
		"status":       design.Status,
		"style":        design.Style,
		"color_scheme": design.ColorScheme,
		"title":        design.Title,
		"description":  design.Description,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to insert into room_designs: %w", err)
	}

	// 2. Insert into default_products
	productsQuery := `
		INSERT INTO design.default_products (
			room_design_id, floor_tile, floor_tile_pattern, toilet, vanity, faucet, mirror,
			lighting, paint, shelving, wall_tile_placement, wall_tile, wall_tile_pattern,
			wallpaper_placement, wallpaper, shower_system, shower_floor_tile,
			shower_floor_tile_pattern, shower_wall_tile, shower_wall_tile_pattern,
			shower_short_wall_tile, shower_glass, niche_tile, tub, tub_filler, tub_door
		) VALUES (
			@room_design_id, @floor_tile, @floor_tile_pattern, @toilet, @vanity, @faucet, @mirror,
			@lighting, @paint, @shelving, @wall_tile_placement, @wall_tile, @wall_tile_pattern,
			@wallpaper_placement, @wallpaper, @shower_system, @shower_floor_tile,
			@shower_floor_tile_pattern, @shower_wall_tile, @shower_wall_tile_pattern,
			@shower_short_wall_tile, @shower_glass, @niche_tile, @tub, @tub_filler, @tub_door
		)
		ON CONFLICT (room_design_id) DO UPDATE SET 
			floor_tile = EXCLUDED.floor_tile, floor_tile_pattern = EXCLUDED.floor_tile_pattern,
			toilet = EXCLUDED.toilet, paint = EXCLUDED.paint,
			vanity = EXCLUDED.vanity, faucet = EXCLUDED.faucet, mirror = EXCLUDED.mirror,
			lighting = EXCLUDED.lighting, shelving = EXCLUDED.shelving,
			wall_tile_placement = EXCLUDED.wall_tile_placement,
			wall_tile = EXCLUDED.wall_tile, wall_tile_pattern = EXCLUDED.wall_tile_pattern,
			wallpaper_placement = EXCLUDED.wallpaper_placement, wallpaper = EXCLUDED.wallpaper,
			shower_floor_tile = EXCLUDED.shower_floor_tile,
			shower_floor_tile_pattern = EXCLUDED.shower_floor_tile_pattern,
			shower_wall_tile = EXCLUDED.shower_wall_tile,
			shower_wall_tile_pattern = EXCLUDED.shower_wall_tile_pattern,
			shower_short_wall_tile = EXCLUDED.shower_short_wall_tile,
			niche_tile = EXCLUDED.niche_tile,
			shower_system = EXCLUDED.shower_system, shower_glass = EXCLUDED.shower_glass,
			tub = EXCLUDED.tub, tub_filler = EXCLUDED.tub_filler, tub_door = EXCLUDED.tub_door
		`
	_, err = tx.Exec(ctx, productsQuery, pgx.NamedArgs{
		"room_design_id":            design.ID,
		"floor_tile":                design.FloorTile,
		"floor_tile_pattern":        design.FloorTilePattern,
		"toilet":                    design.Toilet,
		"vanity":                    design.Vanity,
		"faucet":                    design.Faucet,
		"mirror":                    design.Mirror,
		"lighting":                  design.Lighting,
		"paint":                     design.Paint,
		"shelving":                  design.Shelving,
		"wall_tile_placement":       design.WallTilePlacement,
		"wall_tile":                 design.WallTile,
		"wall_tile_pattern":         design.WallTilePattern,
		"wallpaper_placement":       design.WallpaperPlacement,
		"wallpaper":                 design.Wallpaper,
		"shower_system":             design.ShowerSystem,
		"shower_floor_tile":         design.ShowerFloorTile,
		"shower_floor_tile_pattern": design.ShowerFloorTilePattern,
		"shower_wall_tile":          design.ShowerWallTile,
		"shower_wall_tile_pattern":  design.ShowerWallTilePattern,
		"shower_short_wall_tile":    design.ShowerShortWallTile,
		"shower_glass":              design.ShowerGlass,
		"niche_tile":                design.NicheTile,
		"tub":                       design.Tub,
		"tub_filler":                design.TubFiller,
		"tub_door":                  design.TubDoor,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to insert into default_products: %w", err)
	}

	// 3. Insert into render_prefs
	prefsQuery := `
		INSERT INTO design.render_prefs (
		    room_design_id, shower_glass_visible, tub_door_visible, niches_visible
		)
		VALUES (@room_design_id, @shower_glass_visible, @tub_door_visible, @niches_visible)
		ON CONFLICT (room_design_id)
		DO UPDATE SET
		    shower_glass_visible = EXCLUDED.shower_glass_visible,
		    tub_door_visible = EXCLUDED.tub_door_visible,
		    niches_visible = EXCLUDED.niches_visible
		`
	_, err = tx.Exec(ctx, prefsQuery, pgx.NamedArgs{
		"room_design_id":       design.ID,
		"shower_glass_visible": design.ShowerGlassVisible,
		"tub_door_visible":     design.TubDoorVisible,
		"niches_visible":       design.NichesVisible,
	})
	if err != nil {
		return design.ID, fmt.Errorf("failed to upsert into render_prefs: %w", err)
	}

	if err = tx.Commit(ctx); err != nil {
		return design.ID, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return design.ID, nil
}

// ReadDesign fetches a single Design entity from the database by its ID.
// It joins all related tables to construct the complete Design object.
func (r *RelationalDb) ReadDesign(ctx context.Context, id uuid.UUID) (*usecases.Design, error) {
	query := fmt.Sprintf("%s WHERE rd.id = $1", readDesignBaseQuery)
	row := r.db.QueryRow(ctx, query, id)
	if adapters.IsNil(row) {
		return nil, fmt.Errorf("no design found with id %s", id)
	}
	return scanDesign(row)
}

// DesignsForProject retrieves all designs associated with a given project ID.
func (r *RelationalDb) DesignsForProject(ctx context.Context, projectID string) ([]usecases.Design, error) {
	query := fmt.Sprintf("%s WHERE rd.project_id = $1 ORDER BY rd.updated_at DESC", readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to query designs by project id: %w", err)
	}
	defer rows.Close()

	var designs []usecases.Design
	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			return nil, err
		}
		designs = append(designs, *design)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}
	return designs, nil
}

// DesignsByProject retrieves all designs associated with the given project IDs.
func (r *RelationalDb) DesignsByProject(ctx context.Context, projectIDs []string) (map[string][]usecases.Design, error) {
	projectDesigns := make(map[string][]usecases.Design)
	query := fmt.Sprintf("%s WHERE rd.project_id = ANY($1) ORDER BY rd.project_id, rd.updated_at DESC",
		readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to query designs by project ids (%v): %w", projectIDs, err)
	}
	defer rows.Close()

	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			return nil, err
		}
		projectDesigns[design.ProjectID.String()] = append(projectDesigns[design.ProjectID.String()], *design)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}
	return projectDesigns, nil
}

// DeleteDesign removes a Design entity and its associated data from the database.
// It relies on the ON DELETE CASCADE constraint in the database schema.
// TODO: consider offering only *soft* deletes unless we build an event-sourcing
// that would allows us to reverse deletions.
func (r *RelationalDb) DeleteDesign(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM design.room_designs WHERE id = $1`

	cmdTag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to execute delete command: %w", err)
	}

	if cmdTag.RowsAffected() == 0 {
		return fmt.Errorf("no design found with id %s to delete", id)
	}

	return nil
}
